import React from 'react';

type Doctor = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty: string;
};

type Props = {
  doctor: Doctor;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSave: () => void;
  onCancel: () => void;
};

const EditDoctorForm: React.FC<Props> = ({ doctor, onChange, onSave, onCancel }) => {
  return (
    <div className="bg-white p-4 rounded shadow-md">
      <div className=" grid grid-cols-2 gap-4">
      <div>
        <label className="font-bold">First Name:</label>
        <input
          value={doctor.first_name}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div>
        <label className=" font-bold">Last Name:</label>
      <input
        name="first_name"
        value={doctor.last_name}
        onChange={onChange}
        className="border p-2 w-full mb-2"
        placeholder="First Name"
      />
      </div>
      <div>
      <label className="font-bold">User Name:</label>
      <input
        name="last_name"
        value={doctor.username}
        onChange={onChange}
        className="border p-2 w-full mb-2"
        placeholder="User Name"
      />
      </div>
      <div>
      <label className="font-bold">Email:</label>
      <input
        name="email"
        value={doctor.email}
        onChange={onChange}
        className="border p-2 w-full mb-2"
        placeholder="Email"
      />
      </div>
      <div>
      <label className="font-bold">Specialty:</label>
      <input
        name="  username"
        value={doctor.specialty}
        onChange={onChange}
        className="border p-2 w-full mb-2"
        placeholder="Specialty"
      />
      </div>

      </div>
      <div className="flex gap-2 mt-4">
        <button onClick={onSave} className="bg-blue-600 text-white px-4 py-2 rounded" style={{ backgroundColor: '#EB6309' }}>
          Save Changes
        </button>
        <button onClick={onCancel} className="bg-gray-500 text-white px-4 py-2 rounded">
          Cancel
        </button>
      </div>
    </div>
  );
};

export default EditDoctorForm;
