import axios from 'axios';

export const API = axios.create({
  baseURL: 'http://127.0.0.1:8000',
});

export interface DoctorFormData {
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  role: string;
}

export const registerDoctor = (doctorData: DoctorFormData) => {
  return API.post('/auth/register-user', doctorData);
};
export const getDoctors = () => {
  return API.get('/doctors');
};