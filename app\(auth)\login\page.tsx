import React from 'react'

const page = () => {
  return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 flex-col bg-cover bg-no-repeat bg-center"
      style={{ backgroundImage: "url('/assets/images/BGauth.png')" }}
      >
         {/* Logo Section */}
        <div className="mb-5">
          <img
            src="/assets/images/logo.png"
            alt="Logo"
            className="mx-auto  w-[240px] mb-5"
          />
        </div>
          <div className="bg-white p-8 rounded shadow-md w-full max-w-sm text-center">

        {/* Welcome Heading */}
        <h1 className="text-[38px] leading-[45px] font-bold text-gray-700 mb-5">Welcome to Dashboard</h1>
       
        <h2 className="text-2xl font-semibold mb-6 text-[rgb(235,99,9)]">Login</h2>

        <form className="space-y-4 text-left">
          <div>
            <label className="block text-gray-700 mb-1">Email / Username</label>
            <input
              type="text"
              placeholder="Enter email or username"
              className="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-[rgb(235,99,9)]"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-1">Password</label>
            <input
              type="password"
              placeholder="Enter password"
              className="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-[rgb(235,99,9)]"
            />
          </div>
          <button
            type="submit"
            className="w-full bg-[rgb(235,99,9)] text-white py-2 rounded hover:bg-orange-700 transition duration-200"
          >
            Login
          </button>
        </form>
      </div>
    </div>
  )
}

export default page
