import { FC } from "react";

interface IconMenuDoctorProps {
  className?: string;
}

const IconMenuDoctors: FC<IconMenuDoctorProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      stroke-width="2"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M12 14c4 0 7 3 7 7H5c0-4 3-7 7-7z"
      />
      <circle cx="12" cy="7" r="4" />
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M16 8l3 3m0-3l-3 3"
      />
    </svg>
  );
};

export default IconMenuDoctors;
