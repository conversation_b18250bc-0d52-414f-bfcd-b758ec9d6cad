'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

type Employee = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty?: string;
};

const initialEmployee: Employee[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'amartin' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'rlee' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ng<PERSON>' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'dclark' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'char<PERSON>' },
  { id: 6, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'l<PERSON><PERSON>' },
  { id: 7, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'erobinson' },
  { id: 8, first_name: 'Daniel', last_name: 'Lewis', email: '<EMAIL>', username: 'dlewis' },
  { id: 9, first_name: '<PERSON>', last_name: 'Young', email: '<EMAIL>', username: 'gyoung' },
  { id: 10, first_name: 'Henry', last_name: 'Scott', email: '<EMAIL>', username: 'hscott' },
];

const EditEmployee: React.FC = () => {
  const router = useRouter();
  const [employee, setEmployee] = useState<Employee | null>(null);

  useEffect(() => {
    const storedId = sessionStorage.getItem('editEmployeeId');
    if (!storedId) {
      router.push('/employee');
      return;
    }

    const id = parseInt(storedId);
    const foundEmployee = initialEmployee.find(e => e.id === id);
    if (foundEmployee) {
      setEmployee({ ...foundEmployee });
    } else {
      router.push('/employee');
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!employee) return;
    setEmployee({ ...employee, [e.target.name]: e.target.value });
  };

  const handleSave = () => {
    console.log('Saved Employee:', employee);
    router.push('/employee');
  };

  const handleCancel = () => {
    router.push('/employee');
  };

  if (!employee) return <p>Loading...</p>;

  return (
    <>
      <h2 className="text-2xl font-bold mb-4">Edit Employee</h2>

    <div className="bg-white p-4 rounded shadow-md space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="font-bold">First Name:</label>
          <input
            value={employee.first_name}
            disabled
            className="border p-2 w-full bg-gray-100"
          />
        </div>
        <div>
          <label className="font-bold">Last Name:</label>
          <input
            name="last_name"
            value={employee.last_name}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="font-bold">Username:</label>
          <input
            name="username"
            value={employee.username}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="font-bold">Email:</label>
          <input
            name="email"
            value={employee.email}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
      </div>
      <div className="flex gap-2 mt-4">
        <button
          onClick={handleSave}
          className="bg-[#EB6309] text-white px-4 py-2 rounded"
        >
          Save Changes
        </button>
        <button
          onClick={handleCancel}
          className="bg-gray-500 text-white px-4 py-2 rounded"
        >
          Cancel
        </button>
      </div>
    </div>
    </>
  );
};

export default EditEmployee;
