'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import Modal from '@/components/Modal';
import { useRouter } from 'next/navigation';
import DataTable from 'react-data-table-component';

export type Doctor = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty?: string;
  contact?: string;
};

const initialDoctors: <PERSON>[] = [
{ id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', username: 'johndo<PERSON>' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jane<PERSON>' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: 'micha<PERSON>.<EMAIL>', username: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ed<PERSON><PERSON>' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'wbrown' },
  { id: 6, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'owi<PERSON>' },
  { id: 7, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jtaylor' },
  { id: 8, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'sanderson' },
  { id: 9, first_name: 'Benjamin', last_name: 'Thomas', email: '<EMAIL>', username: 'bthomas' },
  { id: 10, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore' },
  { id: 11, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore' },
  { id: 12, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore' },


];

const DoctorForm: React.FC = () => {
  const [search, setSearch] = useState('');

  const [doctors, setDoctors] = useState<Doctor[]>(initialDoctors);

const handleDelete = (id: number) => {
  const confirmDelete = window.confirm("Are you sure you want to delete this doctor?");
  if (confirmDelete) {
    setDoctors(prev => prev.filter(doctor => doctor.id !== id));
  }
};

  const router = useRouter();

  const handleEdit = (id: number) => {
    // Navigate to the consolidated edit page
    router.push(`/doctor/${id}/edit`);
  };

  const handleview = (id: number) => {
    // Navigate to the view page
    router.push(`/doctor/${id}/view`);
  };

  const filteredDoctors = doctors.filter(doctor =>
    `${doctor.first_name} ${doctor.last_name} ${doctor.email} ${doctor.username}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );
   const columns = [
    { name: 'First Name', selector: (row: Doctor) => row.first_name, sortable: true },
    { name: 'Last Name', selector: (row: Doctor) => row.last_name, sortable: true },
    { name: 'Email', selector: (row: Doctor) => row.email, sortable: true },
    { name: 'Username', selector: (row: Doctor) => row.username, sortable: true },
    {
      name: 'Actions',
      cell: (row: Doctor) => (
        <div className="flex gap-3 items-center">
          <button type="button" onClick={() => handleview(row.id)} title="View">
            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
          </button>
          <button type="button" onClick={() => handleEdit(row.id)} title="Edit">
            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
          </button>
          <button type="button" onClick={() => handleDelete(row.id)} title="Delete">
            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        </div>
      ),
    },
  ];

    return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Doctors List</h2>
        <button
          type="button"
          onClick={() => router.push('/doctor/new')}
          className="bg-orange-600 text-white px-4 py-2 rounded"
        >
          Add Doctor
        </button>
      </div>

      <input
        type="text"
        placeholder="Search by name, email, or username..."
        value={search}
        onChange={e => setSearch(e.target.value)}
        className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
      />

      <DataTable
        columns={columns}
        data={filteredDoctors}
        pagination
        highlightOnHover
        responsive
        striped
        noHeader
      />
    </div>
  );
};

export default DoctorForm;
