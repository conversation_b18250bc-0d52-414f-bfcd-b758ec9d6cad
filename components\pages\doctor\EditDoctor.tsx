'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import EditDoctorForm from './EditDoctorForm';

type Doctor = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty: string;
};

// Dummy doctor data (replace with API call in production)
const doctorData: Doctor[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', username: 'joh<PERSON><PERSON>', specialty: 'Cardiology' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jane<PERSON>', specialty: 'Dermatology' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'm<PERSON><PERSON><PERSON>', specialty: 'Pediatrics' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ed<PERSON><PERSON>', specialty: 'Orthopedics' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'wbrown', specialty: 'Neurology' },
  { id: 6, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'owilson', specialty: 'Radiology' },
  { id: 7, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jtaylor', specialty: 'Cardiology' },
  { id: 8, first_name: 'Sophia', last_name: 'Anderson', email: '<EMAIL>', username: 'sanderson', specialty: 'Dermatology' },
  { id: 9, first_name: 'Benjamin', last_name: 'Thomas', email: '<EMAIL>', username: 'bthomas', specialty: 'Pediatrics' },
  { id: 10, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore', specialty: 'Orthopedics' },
];

const EditDoctor: React.FC = () => {
  const router = useRouter();
  const [doctor, setDoctor] = useState<Doctor | null>(null);

  useEffect(() => {
    const storedId = sessionStorage.getItem('editDoctorId');
    if (!storedId) {
      router.push('/doctor'); // fallback if no ID is found
      return;
    }

    const id = parseInt(storedId);
    const foundDoctor = doctorData.find(d => d.id === id);

    if (foundDoctor) {
      setDoctor({ ...foundDoctor });
    } else {
      router.push('/doctor'); // fallback if invalid ID
    }
  }, []);

  if (!doctor) return <p>Loading...</p>;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDoctor({ ...doctor, [e.target.name]: e.target.value });
  };

  const handleSave = () => {
    console.log('Saved Doctor:', doctor);
    router.push('/doctor');
  };

  const handleCancel = () => {
    router.push('/doctor');
  };

  if (!doctor) return <p>Loading...</p>;

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Edit Doctor</h2>
      <EditDoctorForm
        doctor={doctor}
        onChange={handleChange}
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default EditDoctor;
