'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';


const AddEmployeePage = () => {
  const router = useRouter();

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    username: '',
    specialty: '',
    contact: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Simulate saving employee (e.g., send to API or save to state)
    console.log('New Employee:', formData);

    // Redirect to employee list or show success message
    router.push('/employee'); // adjust this path if needed
  };

  return (
    <div className=" mx-auto bg-white p-6 rounded shadow-md">
      <h2 className="text-2xl font-bold mb-6">Add Employee</h2>
      <form onSubmit={handleSubmit} className=" space-y-4">
        <div className=" grid grid-cols-2 gap-4">
          <div>
          <label className="block mb-1 font-semibold">First Name</label>
          <input
            type="text"
            value={formData.first_name}
            onChange={(e) =>
              setFormData({ ...formData, first_name: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-semibold">Last Name</label>
          <input
            type="text"
            value={formData.last_name}
            onChange={(e) =>
              setFormData({ ...formData, last_name: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>

        <div>
          <label className="block mb-1 font-semibold">Email</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-semibold">User Name</label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) =>
              setFormData({ ...formData, username: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>
        </div>

        <button
          type="submit"
          className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
        >
          Save Employee
        </button>
      </form>
    </div>
  );
};

export default AddEmployeePage;
