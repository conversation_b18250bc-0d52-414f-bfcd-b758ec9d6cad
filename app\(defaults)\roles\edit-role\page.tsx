"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

const permissionsList = ["View", "Add", "Edit", "Delete"];

type Role = {
  id: number;
  name: string;
  permissions: {
    [moduleName: string]: string[];
  };
};
const modules = ["Retainer", "Aligner"];

const roleData: Role[] = [
  {
    id: 1,
    name: "Admin",
    permissions: {
      Retainer: ["View", "Add", "Edit", "Delete"],
      <PERSON><PERSON><PERSON>: ["View", "Add", "Edit"],
    },
  },
  {
    id: 2,
    name: "Doctor",
    permissions: {
      Retainer: ["View", "Add", "Edit"],
      Aligner: ["View", "Add"],
    },
  },
  {
    id: 3,
    name: "Patient",
    permissions: {
      Retainer: ["View"],
      Aligner: ["View"],
    },
  },
  {
    id: 4,
    name: "Employee",
    permissions: {
      Retainer: ["View", "Add"],
      Aligner: ["View"],
    },
  },
  {
    id: 5,
    name: "User",
    permissions: {
      Retainer: ["View"],
      Aligner: ["View"],
    },
  },
];

const EditrolePage = () => {
  const router = useRouter();
  const [formData, setFormData] = useState<Role | null>(null);

  useEffect(() => {
    const storedId = sessionStorage.getItem("editDoctorId");
    if (!storedId) {
      router.push("/roles");
      return;
    }

    const id = parseInt(storedId);
    const foundrole = roleData.find((d) => d.id === id);

    if (foundrole) {
      setFormData(foundrole);
    } else {
      router.push("/roles");
    }
  }, []);

const handleCheckboxChange = (module: string, permission: string) => {
  if (!formData) return;

  const currentModulePermissions = formData.permissions[module] || [];
  let updatedModulePermissions = [...currentModulePermissions];
  const isChecked = currentModulePermissions.includes(permission);

  if (isChecked) {
    // Handle unchecking
    if (permission === "View") {
      // If View is unchecked, remove all permissions for the module
      updatedModulePermissions = [];
    } else {
      // Uncheck specific permission
      updatedModulePermissions = updatedModulePermissions.filter(p => p !== permission);
    }
  } else {
    // Handle checking
    updatedModulePermissions.push(permission);

    // If a non-View permission is being checked, make sure View is included
    if (permission !== "View" && !updatedModulePermissions.includes("View")) {
      updatedModulePermissions.push("View");
    }
  }

  setFormData({
    ...formData,
    permissions: {
      ...formData.permissions,
      [module]: updatedModulePermissions,
    },
  });
};


  if (!formData) return <div>Loading...</div>;

  return (
    <div className="bg-white p-4 rounded shadow-md mx-auto">
      <h2 className="text-2xl font-bold mb-4">Edit Role</h2>

      <div className="mb-4">
        <label className="font-medium">Name:</label>
        <input
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="border p-2 w-full"
        />
      </div>

      <h2 className="text-xl font-bold mb-4">Edit Permissions</h2>
      <div className="mb-4 grid">
        {modules.map((module) => (
          <div key={module} className="mb-4 grid grid-cols-5 gap-5">
            <label className="block mb-2 font-semibold">
              {module} Module
            </label>

            {permissionsList.map((permission) => (
              <label key={permission} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  value={permission}
                  checked={
                    formData.permissions[module]?.includes(permission) || false
                  }
                  onChange={() => handleCheckboxChange(module, permission)}
                  className="accent-[#EB6309]"
                />
                <span>{permission}</span>
              </label>
            ))}
          </div>
        ))}
      </div>

      <button
        onClick={() => router.push("/roles")}
        className="mt-4 bg-[#EB6309] text-white px-4 py-2 rounded"
      >
        Back
      </button>
    </div>
  );
};

export default EditrolePage;
