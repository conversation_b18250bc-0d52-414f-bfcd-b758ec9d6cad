'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

type Doctor = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty: string;
};

// Sample data — replace with API or global state
const doctorData: Doctor[] = [
 { id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', username: 'joh<PERSON><PERSON>', specialty: 'Cardiology' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jane<PERSON>', specialty: 'Dermatology' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'm<PERSON><PERSON><PERSON>', specialty: 'Pediatrics' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ed<PERSON><PERSON>', specialty: 'Orthopedics' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'wbrown', specialty: 'Neurology' },
  { id: 6, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'owilson', specialty: 'Radiology' },
  { id: 7, first_name: 'James', last_name: 'Taylor', email: '<EMAIL>', username: 'jtaylor', specialty: 'Cardiology' },
  { id: 8, first_name: 'Sophia', last_name: 'Anderson', email: '<EMAIL>', username: 'sanderson', specialty: 'Dermatology' },
  { id: 9, first_name: 'Benjamin', last_name: 'Thomas', email: '<EMAIL>', username: 'bthomas', specialty: 'Pediatrics' },
  { id: 10, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore', specialty: 'Orthopedics' },
];

const ViewDoctorPage = () => {
  const router = useRouter();
  const [doctor, setDoctor] = useState<Doctor | null>(null);

  useEffect(() => {
    const storedId = sessionStorage.getItem('editDoctorId');
    if (!storedId) {
      router.push('/doctor');
      return;
    }

    const id = parseInt(storedId);
    const foundDoctor = doctorData.find(d => d.id === id);

    if (foundDoctor) {
      setDoctor(foundDoctor);
    } else {
      router.push('/doctor');
    }
  }, []);

  if (!doctor) return <div>Loading...</div>;

  return (
    <div className="bg-white p-4 rounded shadow-md mx-auto">
      <h2 className="text-2xl font-bold mb-4">Doctor Details</h2>
      <div className=" grid grid-cols-2 gap-4">
      <div className="mb-2">
        <label className="font-medium">First Name:</label>
        <input
          value={doctor.first_name}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div className="mb-2">
        <label className="font-medium">Last Name:</label>
        <input
          value={doctor.last_name}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div className="mb-2">
        <label className="font-medium">Email:</label>
        <input
          value={doctor.email}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div className="mb-2">
        <label className="font-medium">Username:</label>
        <input
          value={doctor.username}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div className="mb-4">
        <label className="font-medium">Specialty:</label>
        <input
          value={doctor.specialty}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
        </div>
        </div>
      <button
        onClick={() => router.push('/doctor')}
        className="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
        style={{ backgroundColor: '#EB6309' }}
      >
        Back
      </button>
    </div>
  );
};

export default ViewDoctorPage;


