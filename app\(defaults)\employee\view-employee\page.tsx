'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

type Employee = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty?: string;
};

// Sample data — replace with API or global state
const initialEmployee: Employee[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'amartin' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'rlee' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ng<PERSON>' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'dclark' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'char<PERSON>' },
  { id: 6, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'l<PERSON><PERSON>' },
  { id: 7, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'erobinson' },
  { id: 8, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'dlewis' },
  { id: 9, first_name: '<PERSON>', last_name: 'Young', email: '<EMAIL>', username: 'gyoung' },
  { id: 10, first_name: 'Henry', last_name: 'Scott', email: '<EMAIL>', username: 'hscott' },
];

const ViewEmployeePage = () => {
  const router = useRouter();
  const [employee, setEmployee] = useState<Employee | null>(null);

  useEffect(() => {
    const storedId = sessionStorage.getItem('editEmployeeId');
    if (!storedId) {
      router.push('/employee');
      return;
    }

    const id = parseInt(storedId);
    const foundEmployee = initialEmployee.find(d => d.id === id);

    if (foundEmployee) {
      setEmployee(foundEmployee);
    } else {
      router.push('/employee');
    }
  }, []);

  if (!employee) return <div>Loading...</div>;

  return (
    <div className="bg-white p-4 rounded shadow-md mx-auto">
      <h2 className="text-2xl font-bold mb-4">Employee Details</h2>
      <div className=" grid grid-cols-2 gap-4">
      <div className="mb-2">
        <label className="font-medium">First Name:</label>
        <input
          value={employee.first_name}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div className="mb-2">
        <label className="font-medium">Last Name:</label>
        <input
          value={employee.last_name}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div className="mb-2">
        <label className="font-medium">Email:</label>
        <input
          value={employee.email}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
      <div className="mb-2">
        <label className="font-medium">Username:</label>
        <input
          value={employee.username}
          disabled
          className="border p-2 w-full bg-gray-100"
        />
      </div>
        </div>
      <button
        onClick={() => router.push('/employee')}
        className="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
        style={{ backgroundColor: '#EB6309' }}
      >
        Back
      </button>
    </div>
  );
};

export default ViewEmployeePage;


