'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Role } from './Roles';

const modules = ['Retainer', 'Aligner'];

// Sample role data - in production this would come from API
const roleData: Role[] = [
  {
    id: 1,
    name: "Admin",
    permissions: {
      Retainer: ["View", "Add", "Edit", "Delete"],
      <PERSON><PERSON><PERSON>: ["View", "Add", "Edit", "Delete"],
    },
  },
  {
    id: 2,
    name: "Doctor",
    permissions: {
      Retainer: ["View", "Add", "Edit"],
      <PERSON><PERSON><PERSON>: ["View", "Add"],
    },
  },
  {
    id: 3,
    name: "Patient",
    permissions: {
      Retainer: ["View"],
      <PERSON><PERSON><PERSON>: ["View"],
    },
  },
  {
    id: 4,
    name: "Employee",
    permissions: {
      Retainer: ["View", "Add"],
      <PERSON><PERSON><PERSON>: ["View"],
    },
  },
  {
    id: 5,
    name: "User",
    permissions: {
      Retainer: ["View"],
      Aligner: ["View"],
    },
  },
];

interface RoleViewProps {
  id: string;
}

const RoleView: React.FC<RoleViewProps> = ({ id }) => {
  const router = useRouter();
  const [role, setRole] = useState<Role | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const roleId = parseInt(id);
    const foundRole = roleData.find(r => r.id === roleId);
    
    if (foundRole) {
      setRole(foundRole);
    } else {
      setError('Role not found');
    }
    setLoading(false);
  }, [id]);

  const handleEdit = () => {
    router.push(`/roles/${id}/edit`);
  };

  const handleBack = () => {
    router.push('/roles');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="text-lg">Loading role details...</div>
      </div>
    );
  }

  if (error || !role) {
    return (
      <div className="mx-auto bg-white p-6 rounded shadow-md">
        <div className="text-red-600 text-center">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p>{error || 'Role not found'}</p>
          <button
            type="button"
            onClick={handleBack}
            className="mt-4 bg-gray-500 text-white px-4 py-2 rounded hover:opacity-90"
          >
            Back to Roles List
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto bg-white p-6 rounded shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Role Details</h2>
        <div className="flex gap-2">
          <button
            type="button"
            onClick={handleEdit}
            className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
          >
            Edit Role
          </button>
          <button
            type="button"
            onClick={handleBack}
            className="bg-gray-500 text-white px-4 py-2 rounded hover:opacity-90"
          >
            Back to List
          </button>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block mb-1 font-semibold text-gray-700">Role Name</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {role.name}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4 text-gray-700">Module Permissions</h3>
          {modules.map((module) => (
            <div key={module} className="mb-4">
              <label className="block mb-2 font-semibold text-gray-600">{module} Module</label>
              <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
                {role.permissions[module]?.length > 0 
                  ? role.permissions[module].join(', ')
                  : 'No permissions assigned'
                }
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RoleView;
