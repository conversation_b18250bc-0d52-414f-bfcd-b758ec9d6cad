'use client';

import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import ApexCharts from 'apexcharts';
let lastDate = new Date().getTime();
const XAXIS_RANGE = 60000; // 60 seconds

// Utility Functions
const generateDataPoint = () => {
  lastDate += 1000;
  return [lastDate, Math.floor(Math.random() * 80) + 10] as [number, number];
};

const getInitialData = (): [number, number][] => {
  const init: [number, number][] = [];
  for (let i = 0; i < 60; i++) {
    init.push(generateDataPoint());
  }
  return init;
};

const DashboardCharts = () => {
    const [areaSeries] = useState([
    {
      name: 'Appointments',
      data: [12, 18, 20, 25, 22, 30, 28, 35, 40, 38, 42, 45],
    },
    {
      name: 'Treatments',
      data: [5, 8, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33],
    },
  ]);

  const areaOptions: ApexOptions = {
    chart: { height: 350, type: 'area', dropShadow: { enabled: true, top: 3, left: 2, blur: 4, opacity: 0.15 } },
    colors: ['#EB6309', '#1E90FF'],
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth' },
    xaxis: { categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
    tooltip: {
      x: { formatter: (val: string | number): string => String(val) },
    },
  };

  // Donut Chart
  const [donutSeries] = useState([35, 25, 50]);
  const donutOptions: ApexOptions = {
    chart: { type: 'donut' },
    labels: ['Doctors', 'Specialists', 'Patients'],
    colors: ['#EB6309', '#1E90FF', '#22C55E'],
    legend: { position: 'bottom' },
    dataLabels: { enabled: false },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: { width: 280 },
          legend: { position: 'bottom' },
        },
      },
    ],
  };

  // Live Line Chart Setup
  const [seriousData, setSeriousData] = useState<[number, number][]>(getInitialData());
  const [normalData, setNormalData] = useState<[number, number][]>(getInitialData());

  const lineOptions: ApexOptions = {
    chart: {
      id: 'realtime',
      type: 'line',
      height: 350,
      animations: {
        enabled: true,
        dynamicAnimation: { speed: 1000 },
      },
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth' },
    title: {
      text: 'Live Serious/Normal Cases',
      align: 'left',
    },
    markers: { size: 0 },
    xaxis: {
      type: 'datetime',
      range: XAXIS_RANGE,
    },
    yaxis: {
      max: 100,
    },
    legend: { show: true },
    colors: ['#EF4444', '#10B981'], // red for serious, green for normal
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const newSerious = generateDataPoint();
      const newNormal = generateDataPoint();

      setSeriousData((prev) => {
        const updated = [...prev, newSerious].slice(-60);
        ApexCharts.exec('realtime', 'updateSeries', [
          { name: 'Serious Cases', data: updated },
          { name: 'Normal Cases', data: normalData },
        ]);
        return updated;
      });

      setNormalData((prev) => {
        const updated = [...prev, newNormal].slice(-60);
        ApexCharts.exec('realtime', 'updateSeries', [
          { name: 'Serious Cases', data: seriousData },
          { name: 'Normal Cases', data: updated },
        ]);
        return updated;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [seriousData, normalData]);

  return (
    <>
      <h2 className="text-2xl font-bold mb-4">Dashboard</h2>

  <div className="p-6 bg-white rounded shadow-md mx-auto space-y-12">
  {/* Area Chart - Full Width */}
  

  {/* Donut & Line Chart - Side by Side */}
  <div className="flex flex-col md:flex-row md:space-x-6 space-y-8 md:space-y-0">
    {/* Donut Chart */}
    <div className="w-full md:w-1/2 bg-white p-4 rounded shadow">
      <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">
        Doctors, Specialists & Patients
      </h2>
      <div className="flex justify-center">
        <Chart options={donutOptions} series={donutSeries} type="donut" height={320} />
      </div>
    </div>

    {/* Line Chart */}
    <div className="w-full md:w-1/2 bg-white p-4 rounded shadow">
      <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">
        Live Serious/Normal Cases
      </h2>
      <Chart
        options={lineOptions}
        series={[
          { name: 'Serious Cases', data: seriousData },
          { name: 'Normal Cases', data: normalData },
        ]}
        type="line"
        height={320}
      />
    </div>
  </div>
  <div>
    <h2 className="text-2xl font-bold text-center mb-4 text-gray-800">
      Monthly Appointments & Treatments
    </h2>
    <Chart options={areaOptions} series={areaSeries} type="area" height={350} />
  </div>
</div>
</>
  );
};

export default DashboardCharts;
