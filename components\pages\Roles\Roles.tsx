"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { EyeIcon, PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";
import DataTable from "react-data-table-component";

type Roles = {
  id: number;
  name: string;
  permissions: string[];
};

const initialRoles: Roles[] = [
  { id: 1, name: "Admin", permissions: ["View", "Add", "Edit", "Delete"] },
  { id: 2, name: "<PERSON>", permissions: ["View", "Add", "Edit"] },
  { id: 3, name: "Patient", permissions: ["View"] },
  { id: 4, name: "Employee", permissions: ["View", "Add", "Edit"] },
  { id: 5, name: "User", permissions: ["View"] },
];

const RolesPermissions: React.FC = () => {
  const [roles, setRoles] = React.useState<Roles[]>(initialRoles);
  const router = useRouter();

  const roleEdit = (id: number) => {
    sessionStorage.setItem("editDoctorId", id.toString());
    router.push("/roles/edit-role");
  };

  const userDelete = (id: number) => {
    setRoles((prev) => prev.filter((role) => role.id !== id));
  };

  const columns = [
    {
      name: "Role Name",
      selector: (row: Roles) => row.name,
      sortable: true,
    },
    {
      name: "View",
      cell: (row: Roles) => (row.permissions.includes("View") ? "True" : "False"),
      center: true,
    },
    {
      name: "Add",
      cell: (row: Roles) => (row.permissions.includes("Add") ? "True" : "False"),
      center: true,
    },
    {
      name: "Edit",
      cell: (row: Roles) => (row.permissions.includes("Edit") ? "True" : "False"),
      center: true,
    },
    {
      name: "Delete",
      cell: (row: Roles) => (row.permissions.includes("Delete") ? "True" : "False"),
      center: true,
    },
    {
      name: "Actions",
      cell: (row: Roles) => (
        <div className="flex items-center gap-3">
          <button title="View">
            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
          </button>
          <button title="Edit" onClick={() => roleEdit(row.id)}>
            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
          </button>
          <button title="Delete" onClick={() => userDelete(row.id)}>
            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
      center: true,
    },
  ];

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Roles List</h2>
        <button
          onClick={() => router.push("/roles/add-role")}
          className="bg-[#EB6309] text-white px-4 py-2 rounded"
        >
          Add Role
        </button>
      </div>

      <DataTable
        columns={columns}
        data={roles}
        pagination
        highlightOnHover
        responsive
        striped
        persistTableHead
        subHeader
        subHeaderComponent={
          <input
            type="text"
            placeholder="Search role name..."
            className="p-2 border rounded w-full sm:w-1/3"
            onChange={(e) => {
              const value = e.target.value.toLowerCase();
              const filtered = initialRoles.filter((role) =>
                role.name.toLowerCase().includes(value)
              );
              setRoles(filtered);
            }}
          />
        }
      />
    </div>
  );
};

export default RolesPermissions;
