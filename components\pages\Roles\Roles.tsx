"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { EyeIcon, PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";
import DataTable from "react-data-table-component";

export type Role = {
  id: number;
  name: string;
  permissions: {
    [moduleName: string]: string[];
  };
};

const initialRoles: Role[] = [
  {
    id: 1,
    name: "Admin",
    permissions: {
      Retainer: ["View", "Add", "Edit", "Delete"],
      <PERSON><PERSON><PERSON>: ["View", "Add", "Edit", "Delete"],
    },
  },
  {
    id: 2,
    name: "Doctor",
    permissions: {
      Retainer: ["View", "Add", "Edit"],
      <PERSON><PERSON><PERSON>: ["View", "Add"],
    },
  },
  {
    id: 3,
    name: "Patient",
    permissions: {
      Retainer: ["View"],
      <PERSON><PERSON>r: ["View"],
    },
  },
  {
    id: 4,
    name: "Employee",
    permissions: {
      Retainer: ["View", "Add"],
      <PERSON>gner: ["View"],
    },
  },
  {
    id: 5,
    name: "User",
    permissions: {
      Retainer: ["View"],
      Aligner: ["View"],
    },
  },
];

const RolesPermissions: React.FC = () => {
  const [roles, setRoles] = React.useState<Role[]>(initialRoles);
  const router = useRouter();

  const roleEdit = (id: number) => {
    router.push(`/roles/${id}/edit`);
  };

  const roleView = (id: number) => {
    router.push(`/roles/${id}/view`);
  };

  const userDelete = (id: number) => {
    const confirmDelete = window.confirm("Are you sure you want to delete this role?");
    if (confirmDelete) {
      setRoles((prev) => prev.filter((role) => role.id !== id));
    }
  };



  const columns = [
    {
      name: "Role Name",
      selector: (row: Role) => row.name,
      sortable: true,
    },
    {
      name: "Retainer Permissions",
      cell: (row: Role) => (
        <div className="text-sm">
          {row.permissions.Retainer?.join(", ") || "None"}
        </div>
      ),
      wrap: true,
    },
    {
      name: "Aligner Permissions",
      cell: (row: Role) => (
        <div className="text-sm">
          {row.permissions.Aligner?.join(", ") || "None"}
        </div>
      ),
      wrap: true,
    },
    {
      name: "Actions",
      cell: (row: Role) => (
        <div className="flex items-center gap-3">
          <button type="button" title="View" onClick={() => roleView(row.id)}>
            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
          </button>
          <button type="button" title="Edit" onClick={() => roleEdit(row.id)}>
            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
          </button>
          <button type="button" title="Delete" onClick={() => userDelete(row.id)}>
            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
      center: true,
    },
  ];

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Roles List</h2>
        <button
          type="button"
          onClick={() => router.push("/roles/new")}
          className="bg-[#EB6309] text-white px-4 py-2 rounded"
        >
          Add Role
        </button>
      </div>

      <DataTable
        columns={columns}
        data={roles}
        pagination
        highlightOnHover
        responsive
        striped
        persistTableHead
        subHeader
        subHeaderComponent={
          <input
            type="text"
            placeholder="Search role name..."
            className="p-2 border rounded w-full sm:w-1/3"
            onChange={(e) => {
              const value = e.target.value.toLowerCase();
              const filtered = initialRoles.filter((role) =>
                role.name.toLowerCase().includes(value)
              );
              setRoles(filtered);
            }}
          />
        }
      />
    </div>
  );
};

export default RolesPermissions;
