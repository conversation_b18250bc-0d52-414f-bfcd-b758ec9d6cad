'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Doctor } from './DoctorCRUDForm';

// Sample doctor data - in production this would come from API
const doctorData: Doctor[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', username: 'joh<PERSON><PERSON>', specialty: 'Cardiology', contact: '123-456-7890' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jane<PERSON>', specialty: 'Dermatology', contact: '123-456-7891' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: 'micha<PERSON>.<EMAIL>', username: 'm<PERSON><PERSON><PERSON>', specialty: 'Pediatrics', contact: '123-456-7892' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ed<PERSON><PERSON>', specialty: 'Orthopedics', contact: '123-456-7893' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'wbrown', specialty: 'Neurology', contact: '123-456-7894' },
];

interface DoctorViewProps {
  id?: string;
}

const DoctorView: React.FC<DoctorViewProps> = ({ id }) => {
  const router = useRouter();
  const [doctor, setDoctor] = useState<Doctor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const doctorId = parseInt(id);
    const foundDoctor = doctorData.find(d => d.id === doctorId);

    if (foundDoctor) {
      setDoctor(foundDoctor);
    } else {
      setError('Doctor not found');
    }
    setLoading(false);
  }, [id]);

  const handleEdit = () => {
    router.push(`/doctor/${id}/edit`);
  };

  const handleBack = () => {
    router.push('/doctor');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="text-lg">Loading doctor details...</div>
      </div>
    );
  }

  if (error || !doctor) {
    return (
      <div className="mx-auto bg-white p-6 rounded shadow-md">
        <div className="text-red-600 text-center">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p>{error || 'Doctor not found'}</p>
          <button
            type="button"
            onClick={handleBack}
            className="mt-4 bg-gray-500 text-white px-4 py-2 rounded hover:opacity-90"
          >
            Back to Doctors List
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto bg-white p-6 rounded shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Doctor Details</h2>
        <div className="flex gap-2">
          <button
            type="button"
            onClick={handleEdit}
            className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
          >
            Edit Doctor
          </button>
          <button
            type="button"
            onClick={handleBack}
            className="bg-gray-500 text-white px-4 py-2 rounded hover:opacity-90"
          >
            Back to List
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block mb-1 font-semibold text-gray-700">First Name</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {doctor.first_name}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Last Name</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {doctor.last_name}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Email</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {doctor.email}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Username</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {doctor.username}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Specialty</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {doctor.specialty || 'Not specified'}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Contact</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {doctor.contact || 'Not specified'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorView;
