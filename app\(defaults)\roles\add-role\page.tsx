'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

const permissionsList = ['View', 'Add', 'Edit', 'Delete'];
const modules = [ 'Aligner', 'Retainer'];

type PermissionsState = {
  [key: string]: string[]; // moduleName: [permissions]
};

const AddRole = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    permissions: {} as PermissionsState,
  });

  const handleCheckboxChange = (module: string, permission: string) => {
    setFormData((prev) => {
      const currentPermissions = prev.permissions[module] || [];
      const alreadySelected = currentPermissions.includes(permission);
      let updatedModulePermissions = [...currentPermissions];

      if (alreadySelected) {
        // Uncheck permission
        updatedModulePermissions = updatedModulePermissions.filter((p) => p !== permission);

        if (permission === 'View') {
          // Uncheck all if View is unchecked
          updatedModulePermissions = [];
        }
      } else {
        // Add permission
        updatedModulePermissions.push(permission);

        if (permission !== 'View' && !updatedModulePermissions.includes('View')) {
          updatedModulePermissions.push('View'); // Ensure View is selected
        }
      }

      return {
        ...prev,
        permissions: {
          ...prev.permissions,
          [module]: updatedModulePermissions,
        },
      };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('New Role:', formData);
    router.push('/roles');
  };

  return (
    <div className="p-6 mx-auto bg-white shadow rounded">
      <h2 className="text-2xl font-bold mb-4">Add New Role</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block mb-1 font-semibold">Role Name</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>

        {modules.map((module) => (
          <div key={module}>
            <div className="grid grid-cols-5 gap-5">
            <label className="block mb-2 font-semibold">{module} Module</label>
              {permissionsList.map((permission) => (
                <label key={permission} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    value={permission}
                    checked={formData.permissions[module]?.includes(permission) || false}
                    onChange={() => handleCheckboxChange(module, permission)}
                    className="accent-[#EB6309]"
                  />
                  <span>{permission}</span>
                </label>
              ))}
            </div>
          </div>
        ))}

        <button
          type="submit"
          className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
        >
          Create Role
        </button>
      </form>
    </div>
  );
};

export default AddRole;
